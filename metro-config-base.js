/**
 * Metro 配置基础模块
 * 包含所有配置相关的工具函数和配置生成器
 */
"use strict";
const p = require("path");
const fs = require("fs");
const YAML = require("yamljs");
const semver = require("semver");

// ==================== 常量定义 ====================
const MAX_PLT_ID = 200000;
const isCMServer = !!(process.env.JOB_URL && process.env.BUILD_NUMBER);

// ==================== 状态变量 ====================
let nextModuleId = {
  pl: 0,
  bu: MAX_PLT_ID,
};

let commonModuleMap = {
  count: 0,
};

let buModuleMap = {
  count: 0,
};

// ==================== 通用工具函数 ====================
function parseYaml(filePath) {
  let result = null;
  try {
    result = YAML.load(filePath);
  } catch (error) {
    throw error;
  }
  return result;
}

// 检查是否是 RAM Bundle
const indexYamlFilePath = p.join(process.cwd(), "./index.yaml");
let isRamBundle = false;

if (fs.existsSync(indexYamlFilePath)) {
  const indexYamlInfo = parseYaml(indexYamlFilePath);
  if (
    indexYamlInfo &&
    indexYamlInfo.bundle_type &&
    indexYamlInfo.bundle_type === "ram-bundle"
  ) {
    isRamBundle = true;
  }
}

// 生成唯一的模块ID
function generateModuleId() {
  // 框架包：使用0-200000范围的ID
  if (global.QRN_PACK_ENV.bundleType !== "biz") {
    return ++nextModuleId.pl;
  }
  // 业务包：使用200000以上的ID
  return ++nextModuleId.bu;
}

// 获取全局扩展依赖配置
function getExtensionDependencies() {
  // 读取扩展依赖映射文件
  const qrnJsPath = p.join(
    process.cwd(),
    "qrn-platform-dependencies-map",
    "ExtensionDependencies.json"
  );
  const extensionDependencies = require(qrnJsPath).extensionDependencies || {};

  // 构建扩展依赖的node_modules路径列表（仅顶层路径）
  const extensionDependenciesNMPath = Object.keys(extensionDependencies).map(
    (key) => {
      return p.join("/node_modules", key);
    }
  );

  return { extensionDependencies, extensionDependenciesNMPath };
}

// ==================== 模块ID处理函数 ====================
function createModuleIdFactory() {
  // 初始化打包的时候获取一下，真正调用的时候才能知道编译平台
  nextModuleId.pl = 0;
  nextModuleId.bu = MAX_PLT_ID;
  let env = global.QRN_PACK_ENV;
  let platform = env.platform;
  let commonMapPath = p.join(
    process.cwd(),
    env.plt_map_dir,
    `platform-${platform}.json`
  );
  if (fs.existsSync(commonMapPath)) {
    commonModuleMap = require(commonMapPath);
    nextModuleId.pl += commonModuleMap.count || 0;
  }

  let buMapPath = p.join(process.cwd(), env.bu_map_dir, `bu-${platform}.json`);
  if (fs.existsSync(buMapPath)) {
    buModuleMap = require(buMapPath);
    nextModuleId.bu += buModuleMap.count || 0;
  }

  let pltBundle = env.bundleType !== "biz";

  return (path) => {
    let pltModuleId;
    path = path.replace(process.cwd(), "");

    if (commonModuleMap && commonModuleMap.count > 0) {
      pltModuleId = commonModuleMap[path];
    }

    // 瘦身包处理
    // 如果是业务打包, 读取 path 中 package.json 的 版本号, 和 extensionDependencies 中的版本号比较, 是否兼容
    if (!pltBundle && pltModuleId) {
      const { extensionDependencies, extensionDependenciesNMPath } =
        getExtensionDependencies();

      // 找出 extensionDependenciesNMPath 中 与 path 匹配的路径, 读取 package.json
      let packageJsonPath = extensionDependenciesNMPath.filter((item) => {
        return path.indexOf(item) === 0;
      });

      if (packageJsonPath.length > 0) {
        packageJsonPath = p.join(
          process.cwd(),
          packageJsonPath[0],
          "package.json"
        );

        if (fs.existsSync(packageJsonPath)) {
          const packageJson = require(packageJsonPath);
          const packageName = packageJson.name;
          const packageVersion = packageJson.version;
          const qExtensionVersion = extensionDependencies[packageName];

          if (packageVersion && qExtensionVersion) {
            if (!semver.eq(packageVersion, qExtensionVersion)) {
              // 如果版本不一致, 则不使用内置的模块 id, 而是使用自增的模块 id
              pltModuleId = null;
              delete commonModuleMap[path];
            }
          }
        }
      }
    }

    let buModuleId;
    if (global.QRN_PACK_ENV.bundleType === "biz" && buModuleMap.count > 0) {
      buModuleId = buModuleMap[path];
    }

    if (pltModuleId) {
      if (pltModuleId >= 0 && pltModuleId <= MAX_PLT_ID) {
        return pltModuleId;
      } else {
        throw new Error("公共方法数将要超额");
      }
    } else if (buModuleId) {
      if (buModuleId >= MAX_PLT_ID) {
        return buModuleId;
      } else {
        throw new Error(`业务Id不允许小于 ${MAX_PLT_ID}`);
      }
    } else {
      let moduleId = generateModuleId();
      if (pltBundle) {
        commonModuleMap[path] = commonModuleMap.count = moduleId;
      } else {
        buModuleMap.count = moduleId - MAX_PLT_ID;
        buModuleMap[path] = moduleId;
      }

      return moduleId;
    }
  };
}

// 添加静态方法
createModuleIdFactory.getMap = () => {
  if (global.QRN_PACK_ENV.bundleType !== "biz") {
    return commonModuleMap;
  } else {
    return buModuleMap;
  }
};

createModuleIdFactory.isPltBundle = () => {
  return global.QRN_PACK_ENV.bundleType !== "biz";
};

createModuleIdFactory.getMapDir = () => {
  const env = global.QRN_PACK_ENV;
  return env.bundleType === "biz"
    ? p.join(process.cwd(), env.bu_map_dir, `bu-${env.platform}.json`)
    : p.join(process.cwd(), env.plt_map_dir, `platform-${env.platform}.json`);
};

createModuleIdFactory.getMapOutputDir = () => {
  const env = global.QRN_PACK_ENV;
  return env.bundleType === "biz"
    ? p.join(process.cwd(), env.bu_map_output, `bu-${env.platform}.json`)
    : p.join(
        process.cwd(),
        env.plt_map_output,
        `platform-${env.platform}.json`
      );
};

// 过滤不需要打包的模块 - 仅在业务包打包时生效
function processModuleFilter(module) {
  // 如果是框架包打包，包含所有模块
  if (global.QRN_PACK_ENV.bundleType !== "biz") {
    return true;
  } else {
    // 业务包打包时，需要过滤掉框架代码
    let path = module.path.replace(process.cwd(), "");

    if (
      path.indexOf("__prelude__") !== -1 || // 过滤Metro预加载代码（运行时环境初始化）
      path.indexOf("/node_modules/react-native/Libraries/polyfills") !== -1 || // 过滤RN核心polyfill（框架兼容性补丁）
      // path.indexOf('require-/') != -1 || // [已注释] 过滤require入口控制代码
      /sourceMappingURL|\.map$|\.map\.js$/.test(path) || // 过滤source map文件和调试映射注释
      path.indexOf("/node_modules/metro/src/lib/polyfills/") !== -1 || // 过滤Metro polyfill（RN63版本）
      path.indexOf("/node_modules/metro-runtime/src/polyfills/") !== -1 || // 过滤Metro运行时polyfill（RN68版本）
      path.indexOf("/node_modules/@react-native/polyfills/") !== -1 || // 过滤新版RN polyfill
      path.indexOf("/node_modules/@react-native/js-polyfills") !== -1 // 过滤RN JS polyfill（RN68版本）

    ) {
      // 匹配以上条件的模块不包含在业务包中（这些都是框架代码）
      return false;
    }

    // 检查模块是否已存在于公共模块映射中，如果存在则排除（避免重复打包）
    return !commonModuleMap[path];
  }
}

// 获取在主模块之前需要运行的模块列表
function getModulesRunBeforeMainModule(entryFilePath) {
  // 框架包需要预先加载核心初始化模块
  if (global.QRN_PACK_ENV.bundleType !== "biz") {
    return ["InitializeCore"];
  }
  // 业务包不需要预加载模块（依赖框架包提供的基础环境）
  return [];
}

// ==================== 转换选项函数 ====================
// 解析 Babel 配置获取转换选项
function getTransformOption(entryFilePath) {
  let projectBabelRCPath = "",
    projectRoot = "",
    unstable_disableES6Transforms = false,
    experimentalImportSupport = false;

  if (entryFilePath && entryFilePath[0]) {
    projectRoot = p.dirname(entryFilePath[0]);
    projectBabelRCPath = p.resolve(projectRoot, ".babelrc");
  }

  if (projectBabelRCPath) {
    // 尝试不同的babel配置文件
    if (!fs.existsSync(projectBabelRCPath)) {
      projectBabelRCPath = p.resolve(projectRoot, ".babelrc.js");
    }
    if (!fs.existsSync(projectBabelRCPath)) {
      projectBabelRCPath = p.resolve(projectRoot, "babel.config.js");
    }

    if (fs.existsSync(projectBabelRCPath)) {
      try {
        const file = fs.readFileSync(projectBabelRCPath, "utf-8");
        const data = JSON.parse(file);

        for (let i = 0; i < data.presets.length; i++) {
          const element = data.presets[i];

          if (element instanceof Array) {
            if (element[0] === "module:metro-react-native-babel-preset") {
              if (element[1]["disableImportExportTransform"]) {
                experimentalImportSupport =
                  element[1]["disableImportExportTransform"];
              }
              if (element[1]["unstable_disableES6Transforms"]) {
                unstable_disableES6Transforms =
                  element[1]["unstable_disableES6Transforms"];
              }
            }
          }
        }
      } catch (error) {
        throw new Error(
          ".babelrc 解析失败 :" + projectBabelRCPath + ". 信息: ",
          error.message
        );
      }
    }
  }

  const result = { unstable_disableES6Transforms, experimentalImportSupport };

  if (
    isRamBundle &&
    global.QRN_PACK_ENV &&
    global.QRN_PACK_ENV.isRamBundle &&
    global.QRN_PACK_ENV.isRamBundle === "true"
  ) {
    result["inlineRequires"] = true;
  }

  return result;
}

// ==================== RAM Bundle 功能 ====================
// 获取预加载模块
function getPreloadedModules() {
  const preloadedModulesFilePath = p.join(
    process.cwd(),
    "./preloadedModules.js"
  );

  if (!fs.existsSync(preloadedModulesFilePath)) {
    fs.writeFileSync(
      preloadedModulesFilePath,
      `module.exports = [
                'index.js',
                'index_replace_plt_android.js',
                'index_replace_plt_ios.js'
            ];`
    );
  }

  const modulePaths = require(preloadedModulesFilePath);

  // 用于 ram-bundle, 处理预加载模块
  const moduleMap = {};
  (modulePaths || []).forEach((path) => {
    if (fs.existsSync(path)) {
      moduleMap[p.resolve(path)] = true;
    }
  });

  return moduleMap;
}

// ==================== 配置生成函数 ====================
function customCacheStores() {
  // 在函数首次调用时就保存路径值，而不是引用环境变量
  const cachePath = process.env.QRN_METRO_CACHE;

  // 目录不存在则创建
  if (!fs.existsSync(cachePath)) {
    fs.mkdirSync(cachePath, { recursive: true });
  }

  console.log(`使用缓存目录: ${cachePath}，缓存时间: 1天`);

  return ({ FileStore }) => {
    return [
      new FileStore({
        root: cachePath,
        ttl: 1000 * 60 * 60 * 24,
      }),
    ];
  };
}

// 导出基础配置函数 - 标准模式
function createBaseConfig(isLocal = false) {
  // 本地模式直接返回空配置
  if (!isCMServer) {
    return {};
  }

  const config = {
    transformer: {
      getTransformOptions: (entryFilePath) => {
        const transform = getTransformOption(entryFilePath);
        const result = { transform };

        if (isRamBundle) {
          result["preloadedModules"] = getPreloadedModules();
        }

        return result;
      },
    },
    serializer: {
      createModuleIdFactory,
      processModuleFilter,
      getModulesRunBeforeMainModule,
    },
  };

  // 如果存在 QRN_METRO_CACHE 环境变量，添加空缓存存储配置
  if (process.env.QRN_METRO_CACHE) {
    config.cacheStores = customCacheStores();
  }

  return config;
}

// 导出基础配置函数 - RN72 模式
function createRN72Config(isLocal = false) {
  // 本地模式直接返回空配置
  if (!isCMServer) {
    return {};
  }

  const config = {
    transformer: {
      getTransformOptions: async () => ({
        transform: {
          experimentalImportSupport: true,
          inlineRequires: true,
        },
      }),
    },
    serializer: {
      createModuleIdFactory,
      processModuleFilter,
      getModulesRunBeforeMainModule,
    },
  };

  // 如果存在 QRN_METRO_CACHE 环境变量，添加空缓存存储配置
  if (process.env.QRN_METRO_CACHE) {
    config.cacheStores = customCacheStores();
  }

  return config;
}

// 导出基础配置函数 - RN77 模式
function createRN77Config(isLocal = false) {
  // 本地模式直接返回空配置
  if (!isCMServer) {
    return {};
  }

  // TODO 先保持跟 rn72 一致
  const config = {
    transformer: {
      getTransformOptions: async () => ({
        transform: {
          experimentalImportSupport: true,
          inlineRequires: true,
        },
      }),
    },
    serializer: {
      createModuleIdFactory,
      processModuleFilter,
      getModulesRunBeforeMainModule,
    },
  };

  // 如果存在 QRN_METRO_CACHE 环境变量，添加空缓存存储配置
  if (process.env.QRN_METRO_CACHE) {
    config.cacheStores = customCacheStores();
  }

  return config;
}

// 获取本地 metro.config.js
async function getLocalMetroConfig() {
  const localMetroConfigPath = p.join(process.cwd(), "metro.config.js");

  // 如果是CI环境，直接返回空对象
  if (isCMServer) {
    return {};
  }

  if (fs.existsSync(localMetroConfigPath)) {
    const context = require(localMetroConfigPath);

    // 返回的配置可能是函数格式，本身 metro 也支持直接读取这种格式
    if (typeof context === "function") {
      try {
        const result = context();
        if (result instanceof Promise) {
          return await result; // 获取异步获取的配置
        }
        return result; // 返回同步获取的配置
      } catch (error) {
        console.error("getLocalMetroConfig 执行时报错", error);
        return {};
      }
    }
    return context; // 直接返回 obj
  }

  return {};
}

// 获取 metro_bu.config.js
async function getBuMetroConfig() {
  const buMetroConfigPath = p.join(process.cwd(), "metro_bu.config.js");

  if (fs.existsSync(buMetroConfigPath)) {
    const context = require(buMetroConfigPath);

    // 返回的配置可能是函数格式，本身 metro 也支持直接读取这种格式
    if (typeof context === "function") {
      try {
        const result = context();
        if (result instanceof Promise) {
          return await result; // 获取异步获取的配置
        }
        return result; // 返回同步获取的配置
      } catch (error) {
        console.error("getBuMetroConfig 执行时报错", error);
        return {};
      }
    }
    return context; // 直接返回 obj
  }

  return {};
}

// ==================== 导出 ====================
module.exports = {
  // 常量
  MAX_PLT_ID,
  isRamBundle,

  // 工具函数
  parseYaml,
  generateModuleId,
  getExtensionDependencies,

  // 序列化器相关
  createModuleIdFactory,
  processModuleFilter,
  getModulesRunBeforeMainModule,

  // 转换器相关
  getTransformOption,
  getPreloadedModules,

  // 配置生成器
  createBaseConfig,
  createRN72Config,
  createRN77Config,

  // 获取本地 metro.config.js
  getLocalMetroConfig,

  // 获取 metro_bu.config.js
  getBuMetroConfig,
};
