/**
 * method
 * logger.log('hello');
 * logger.trace('hello', 'world');
 * logger.debug('hello %s',  'world', 123);
 * logger.info('hello %s %d',  'world', 123, {foo:'bar'});
 * logger.warn('hello %s %d %j', 'world', 123, {foo:'bar'});
 * logger.error('hello %s %d %j', 'world', 123, {foo:'bar'}, [1, 2, 3, 4], Object);
 *
 * level
 * 'log':0 (default), 'trace':1, 'debug':2, 'info':3, 'warn':4, 'error':5, 'fatal':6
 */
const qLevel = process.env.Q_PACK_LOG_LEVEL

const level = qLevel ? +qLevel : 9

const qlog = require('tracer').colorConsole({
  level,
  format : [
    "\n{{timestamp}} <{{title}}> (in {{file}}:{{line}}) {{message}}", //default format
    {
      error : "{{timestamp}} <{{title}}> (in {{file}}:{{line}}) {{message}}\nCall Stack:\n{{stack}}" // error format
    }
  ]
});

global.qlog = module.exports = qlog
