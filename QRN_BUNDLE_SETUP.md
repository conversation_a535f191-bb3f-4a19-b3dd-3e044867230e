# QRN Bundle 命令设置指南

## 问题诊断

如果你遇到以下错误：
```
Warning: QRN commands not found, QRN bundle command may not be available
error: unknown command 'qrn-bundle'
```

这表示 React Native CLI 没有找到 QRN 自定义命令配置。

## 解决方案

### 1. 确保正确安装依赖

确保你的项目已安装 `@qnpm/QRNPackager`：

```bash
npm install @qnpm/QRNPackager
# 或
yarn add @qnpm/QRNPackager
```

### 2. 创建 react-native.config.js 文件

在你的**项目根目录**（与 package.json 同级）创建 `react-native.config.js` 文件：

```javascript
/**
 * React Native CLI 配置文件
 * 用于注册 QRN 自定义命令
 */

let commands = [];

try {
  // 尝试加载 QRN 命令
  const qrnPackager = require('@qnpm/QRNPackager');
  if (qrnPackager && qrnPackager.commands) {
    commands = qrnPackager.commands;
    console.log('✅ QRN commands loaded successfully');
  } else {
    // 如果包结构不同，尝试其他路径
    const { commands: qrnCommands } = require('@qnpm/QRNPackager/qrn/commands');
    commands = qrnCommands;
    console.log('✅ QRN commands loaded from alternative path');
  }
} catch (error) {
  console.warn('⚠️  Warning: QRN commands not found, QRN bundle command may not be available');
  console.warn('   Please ensure @qnpm/QRNPackager is properly installed');
  console.warn('   Error:', error.message);
}

/**
 * @type {import("@react-native-community/cli-types").Config}
 */
const config = {
  commands,
};

module.exports = config;
```

### 3. 验证配置

运行以下命令验证配置是否正确：

```bash
npx react-native --help
```

你应该能看到 `qrn-bundle` 命令在可用命令列表中。

### 4. 使用 QRN Bundle 命令

现在你可以使用 QRN bundle 命令：

```bash
npx react-native qrn-bundle --platform ios --dev false --bundle-type platform --output-dir ./dist/jsBundle --bundle-output ./dist/jsBundle/platform.ios.min.js --buildTag v8.0.0-beta.1 --pltmap-output ./dist --pltmap-dir ../tmp/latest-dependencies-map --config ./qrn.metro.config.js
```

### 5. 常见参数说明

| 参数 | 描述 | 示例 |
|------|------|------|
| `--platform` | 目标平台 | `ios`, `android` |
| `--bundle-type` | 打包类型 | `biz`, `platform` |
| `--output-dir` | 输出目录 | `./dist/jsBundle` |
| `--bundle-output` | Bundle 文件输出路径 | `./dist/jsBundle/platform.ios.min.js` |
| `--buildTag` | 构建标签 | `v8.0.0-beta.1` |
| `--pltmap-output` | 平台映射输出目录 | `./dist` |
| `--pltmap-dir` | 平台映射源目录 | `../tmp/latest-dependencies-map` |
| `--config` | Metro 配置文件 | `./qrn.metro.config.js` |

## 故障排除

### 问题1：命令仍然不可用
- 确保 `react-native.config.js` 在项目根目录
- 检查 `@qnpm/QRNPackager` 是否正确安装
- 尝试删除 `node_modules` 并重新安装

### 问题2：模块加载失败
- 检查 `@qnpm/QRNPackager` 的版本
- 确保路径引用正确
- 查看控制台的详细错误信息

### 问题3：权限问题
- 确保有足够的文件系统权限
- 在某些系统上可能需要使用 `sudo`（不推荐）

## 联系支持

如果问题仍然存在，请提供以下信息：
- Node.js 版本
- React Native CLI 版本
- `@qnpm/QRNPackager` 版本
- 完整的错误日志
- 项目目录结构
