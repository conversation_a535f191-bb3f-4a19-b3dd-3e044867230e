/**
 * 自定义Bundle命令
 * 基于React Native CLI的bundle命令，添加了QRN特有的参数和逻辑
 */

// 使用浅层引用，更稳定
const { commands } = require('@react-native-community/cli-plugin-metro');
const path = require('path');

// 从原始bundle命令获取配置
const originalBundleCommand = commands.find(cmd => cmd.name === 'bundle');
const bundleCommandLineArgs = originalBundleCommand ? originalBundleCommand.options : [];

// 自定义参数定义
const customOptions = [
  {
    name: '--output-dir <string>',
    description: '打包产物输出目录',
    default: '.'
  },
  {
    name: '--bundle-type <string>',
    description: '打包类型: biz 或 platform',
    default: 'biz'
  },
  {
    name: '--build-tag <string>',
    description: 'Git构建标签'
  },
  {
    name: '--qpinfo <string>',
    description: 'QP信息配置',
    default: '{}'
  },
  {
    name: '--pltmap-dir <string>',
    description: '平台映射目录',
    default: '.'
  },
  {
    name: '--bumap-dir <string>',
    description: '业务映射目录',
    default: '.'
  },
  {
    name: '--pltmap-output <string>',
    description: '平台映射输出目录',
    default: '.'
  },
  {
    name: '--bumap-output <string>',
    description: '业务映射输出目录',
    default: '.'
  }
];

// 合并原生bundle命令参数和自定义参数
const allOptions = [...bundleCommandLineArgs, ...customOptions];

// 获取原始bundle命令的执行函数
const originalBundleFunc = originalBundleCommand ? originalBundleCommand.func : null;

/**
 * 自定义bundle命令实现
 */
const customBundleCommand = {
  name: 'bundle',
  description: 'QRN bundle 命令，用于构建React Native bundle。',
  options: allOptions,

  func: async (argv, config, args) => {
    console.log('🚀 开始执行 qrn bundle 命令...');

    // 提取自定义参数
    const customArgs = {
      outputDir: args.outputDir || '.',
      bundleType: args.bundleType || 'biz',
      buildTag: args.buildTag,
      qpinfo: args.qpinfo || '{}',
      pltmapDir: args.pltmapDir || '.',
      bumapDir: args.bumapDir || '.',
      pltmapOutput: args.pltmapOutput || '.',
      bumapOutput: args.bumapOutput || '.'
    };

    console.log('📋 自定义参数:', customArgs);

    // 处理输出路径
    if (customArgs.bundleType && customArgs.outputDir) {
      // 如果没有指定bundle-output，根据自定义参数生成
      if (!args.bundleOutput) {
        const hybridid = 'demo'; // 可以从配置文件读取
        args.bundleOutput = path.join(
          customArgs.outputDir,
          `${customArgs.bundleType}.${hybridid}.${args.platform}.bundle`
        );
      }

      // 如果没有指定sourcemap-output，根据自定义参数生成
      if (!args.sourcemapOutput) {
        const hybridid = 'demo'; // 可以从配置文件读取
        args.sourcemapOutput = path.join(
          customArgs.outputDir,
          `${customArgs.bundleType}.${hybridid}.${args.platform}.map`
        );
      }

      // 设置assets输出目录
      if (!args.assetsDest) {
        args.assetsDest = customArgs.outputDir;
      }
    }

    console.log('📦 Bundle输出路径:', args.bundleOutput);
    console.log('🗺️  SourceMap输出路径:', args.sourcemapOutput);
    console.log('🖼️  Assets输出目录:', args.assetsDest);

    // 设置全局环境变量供其他模块使用
    global.QRN_PACK_ENV = {
      platform: args.platform,
      bundleType: customArgs.bundleType,
      plt_map_dir: customArgs.pltmapDir,
      plt_map_output: customArgs.pltmapOutput,
      bu_map_dir: customArgs.bumapDir,
      bu_map_output: customArgs.bumapOutput,
      buildTag: customArgs.buildTag
    };

    // 如果没有指定config，使用默认的metro.config.js
    if (!args.config) {
      args.config = './metro.config.js';
    }

    try {
      // 调用原始bundle命令
      console.log('⚡ 开始构建bundle...');

      if (!originalBundleFunc) {
        throw new Error('无法找到原始bundle命令');
      }

      // 调用原始bundle命令的执行函数
      const result = await originalBundleFunc([], config, args);

      // 这里可以添加构建后的自定义处理逻辑
      console.log('✅ Bundle构建完成');

      // 可以在这里添加签名、生成staticAssets.json等后处理逻辑
      await postBundleProcessing(customArgs);

      return result;
    } catch (error) {
      console.error('❌ Bundle构建失败:', error.message);
      throw error;
    }
  }
};

/**
 * Bundle构建后的处理逻辑
 */
async function postBundleProcessing(customArgs) {
  console.log('🔧 执行构建后处理...');

  // 这里可以添加：
  // 1. 文件签名
  // 2. 生成staticAssets.json
  // 3. 其他自定义后处理逻辑

  if (customArgs.buildTag) {
    console.log(`🏷️  构建标签: ${customArgs.buildTag}`);
  }

  console.log('✨ 构建后处理完成');
}

// 导出自定义bundle命令
module.exports = { customBundleCommand };