/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *  strict
 * @format
 */
"use strict";
const { getDefaultConfig } = require("@react-native/metro-config");
const { mergeConfig } = require("metro-config");
const {
  createHarmonyMetroConfig,
} = require("react-native-harmony/metro.config");

// 导入基础配置创建函数,
const { createRN72Config, getLocalMetroConfig, getBuMetroConfig } = require("@qnpm/QRNPackager/metro-config-base");

// 获取基础配置
const baseConfig = createRN72Config();

// 创建 Harmony 配置
const harmonyConfig = createHarmonyMetroConfig({
  reactNativeHarmonyPackageName: "react-native-harmony",
});

// 合并所有配置
module.exports = (async () => {
  // 获取异步的本地配置
  const localConfig = await getLocalMetroConfig();
  // 获取异步的业务配置
  const buConfig = await getBuMetroConfig();

  return mergeConfig(
    getDefaultConfig(process.cwd()),
    mergeConfig(harmonyConfig, baseConfig),
    localConfig,
    buConfig
  );
})();
