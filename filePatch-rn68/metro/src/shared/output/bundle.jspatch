/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 */
'use strict';
// QRN ADD
let createModuleFactory;
// QRN END
const relativizeSourceMapInline = require('../../lib/relativizeSourceMap');

const Server = require('../../Server');

const writeFile = require('./writeFile');

function buildBundle(packagerClient, requestOptions) {
    createModuleFactory = packagerClient._config.serializer.createModuleIdFactory;
    return packagerClient.build({
        ...Server.DEFAULT_BUNDLE_OPTIONS,
        ...requestOptions,
        bundleType: 'bundle',
    });
}

function relativateSerializedMap(map, sourceMapSourcesRoot) {
    const sourceMap = JSON.parse(map);
    relativizeSourceMapInline(sourceMap, sourceMapSourcesRoot);
    return JSON.stringify(sourceMap);
}

async function saveBundleAndMap(bundle, options, log) {
    const { bundleOutput, bundleEncoding: encoding, sourcemapOutput, sourcemapSourcesRoot } = options;
    const writeFns = [];
    writeFns.push(async () => {
        log('Writing bundle output to:', bundleOutput);
        await writeFile(bundleOutput, bundle.code, encoding);
        log('Done writing bundle output');
    });

    if (sourcemapOutput) {
        let { map } = bundle;

        if (sourcemapSourcesRoot !== undefined) {
            log('start relativating source map');
            map = relativateSerializedMap(map, sourcemapSourcesRoot);
            log('finished relativating');
        }

        writeFns.push(async () => {
            log('Writing sourcemap output to:', sourcemapOutput);
            await writeFile(sourcemapOutput, map, null);
            log('Done writing sourcemap output');
        });
    } // Wait until everything is written to disk.

    // QRN ADD
    // 针对qrn dependencies_map 修改; 发布完导出新依赖映射到 TargetDir
    if (createModuleFactory.getMapOutputDir) {
        let moduleMapPath = createModuleFactory.getMapOutputDir();

        writeFns.push(async function () {
            log('Writing moduleMap output to:', moduleMapPath);
            await writeFile(moduleMapPath, JSON.stringify(createModuleFactory.getMap()), encoding);
            log('Done writing moduleMap output');
        });
    }
    // QRN END

    await Promise.all(writeFns.map((cb) => cb()));
}

exports.build = buildBundle;
exports.save = saveBundleAndMap;
exports.formatName = 'bundle';
