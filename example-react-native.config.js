/**
 * React Native CLI 配置文件
 * 用于注册 QRN 自定义命令
 * 
 * 使用方法：
 * 1. 将此文件复制到你的项目根目录
 * 2. 重命名为 react-native.config.js
 * 3. 确保已安装 @qnpm/QRNPackager 包
 */

let commands = [];

try {
  // 尝试加载 QRN 命令
  const qrnPackager = require('@qnpm/QRNPackager');
  if (qrnPackager && qrnPackager.commands) {
    commands = qrnPackager.commands;
    console.log('✅ QRN commands loaded successfully');
  } else {
    // 如果包结构不同，尝试其他路径
    const { commands: qrnCommands } = require('@qnpm/QRNPackager/qrn/commands');
    commands = qrnCommands;
    console.log('✅ QRN commands loaded from alternative path');
  }
} catch (error) {
  console.warn('⚠️  Warning: QRN commands not found, QRN bundle command may not be available');
  console.warn('   Please ensure @qnpm/QRNPackager is properly installed');
  console.warn('   Error:', error.message);
}

/**
 * @type {import("@react-native-community/cli-types").Config}
 */
const config = {
  commands,
};

module.exports = config;
