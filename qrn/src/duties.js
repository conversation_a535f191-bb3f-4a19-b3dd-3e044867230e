/**
 * <AUTHOR>
 * @description QRN 打包流程
 */
let utils = require('./utils'),
    fs = require('fs'),
    path = require('path'),
    chalk = require('chalk'),
    getTTFFontName = require('./lib/getTTFFontName'),
    sign = require('./lib/sign.js'),
    { spawnSync } = require('child_process'),
    Promise = require('promise');

let sigaContent = '';
let jsFiles = [];

function generatedEntry(buEntryFile, signature) {
    // 仅 beta 下, 初始化模块请求统计
    let modulesRequire = '';

    if (process.env.deployType === 'beta') {
        modulesRequire = 'global.__q_requireModuleIds = new Set();';
    }

    return `
        try {
            ${modulesRequire}
            let NativeModules = require('react-native').NativeModules;
            global.QNativeRequire = NativeModules.QRCTNativeRequire;
            ${signature}
            require('${path.join(utils.getProjectRoots(), buEntryFile)}');
            NativeModules.QAV && NativeModules.QAV.logStatic && NativeModules.QAV.logStatic(__QP_INFO);
            NativeModules.QRNQPInfoConfig && NativeModules.QRNQPInfoConfig.setJSBundleQPInfo(__QP_INFO);
        } catch(err) {
            throw err
        }
    `;
}

let validateProject = (module.exports = {
    /**
     * @method 将 shell 输出格式化为数组
     */
    _formatShellOuput: (res) => res.trim().split(/[\r\n]+/g),
    /**
     * @method 获取 shell 执行结果第一行
     */
    _getFirstItem: (res, msg) => {
        res = (validateProject._formatShellOuput(res)[0] || '').trim();
        if (!res) {
            throw Error(msg);
        } else {
            return res;
        }
    },
    /**
     * @method 通过 shell 读取 providesModule
     * @description 文件的校验不再是必须，execSync 执行结果为空，居然也抛出错误，我也是醉了
     */
    _getProvidesModuleFiles: (name) => {
        return new Promise(function (rs, rj) {
            utils
                .execSync('grep -r "@providesModule ' + name + '" -l --exclude-dir qrn --exclude-dir node_modules .')
                .then(
                    (res) => {
                        let roots = utils.getProjectRoots();
                        res = res.trim().split(/[\r\n]+/g);
                        rs(res.map((p) => path.join(roots, p)));
                    },
                    function () {
                        rs([]);
                    }
                );
        });
    },
    copyConfig: (options) => {
        let dConfigPath = path.join(utils.getProjectRoots(), './qrn.metro.config.js');
        let oConfigPath = path.join(__dirname, '../../qrn.metro.config.js');
        // 改成强制覆盖
        if (!fs.existsSync(dConfigPath)) {
            qlog.info('复制配置文件');
            if (!fs.existsSync(oConfigPath)) {
                qlog.info('找不到配置文件!请联系 QRN 框架组!');
            }
        }

        fs.copyFileSync(oConfigPath, dConfigPath);

        return options;
    },
    relpaceEnterenceFile: (options) => {
        let { entryFile, qpInfo, bundleType, buildTag } = options;
        if (bundleType === 'platform') {
            // 框架包不动
            return options;
        }
        qlog.info('替换业务代码入口');
        if (!entryFile) utils.handleError(Error('index.js Not Found'));
        let roots = utils.getProjectRoots();

        // const replaceEntryName = 'index_replace_plt.js';
        const qpInfoData = JSON.parse(qpInfo);
        const replaceEntryName = `index_replace_plt_${qpInfoData.platform}.js`;
        let buEntryFile = entryFile;
        entryFile = replaceEntryName;

        qlog.info('获取业务代码签名...');
        let signature = sign('\n', {
            bundleType,
            buildTag,
            qpInfo
        }).trim();

        // 获取生成的签名值，用于校验
        const pattern = /SignedSource<<(\w*)>>/g;
        let group = pattern.exec(signature);
        if (group.length >= 2) {
            sigaContent = group[1];
        }

        // 生成新入口
        let generatedCode = generatedEntry(buEntryFile, signature);

        qlog.info('追加新的入口与签名中....');
        fs.writeFileSync(path.join(roots, entryFile), generatedCode);
        let hasReplaceEntry = true;
        return {
            ...options,
            entryFile,
            hasReplaceEntry
        };
    },
    /**
     * @method 校验 HybridId
     */
    validateHybridId: (options) => {
        qlog.debug('validateHybridId():::options', options);
        let { entryFile, bundleType, buildTag } = options;
        return utils.execSync('find . -path "./node_modules" -prune -o -name index.yaml -print').then((res) => {
            let roots = utils.getProjectRoots(),
                indexYAMLFile;
            // 第一个或者同目录下的 index.yaml
            if (entryFile) {
                validateProject._formatShellOuput(res).forEach((item, index) => {
                    item = item.trim();
                    if (
                        index === 0 ||
                        path.relative('.', path.dirname(entryFile)) === path.relative('.', path.dirname(item))
                    ) {
                        indexYAMLFile = path.join(roots, item);
                    }
                });
            } else {
                indexYAMLFile = path.join(
                    roots,
                    validateProject._getFirstItem(res, 'index.yaml Not Found in ' + roots)
                );
            }
            if (!indexYAMLFile) utils.handleError(Error('index.yaml Not Found'));
            if (!entryFile) entryFile = path.join(path.dirname(indexYAMLFile), 'index.js');

            qlog.debug('entryFile:', entryFile);
            return utils.readFileSync(indexYAMLFile).then((content) => {
                qlog.debug('index.yaml文件内容为:\n', content);
                let hybridid;
                content.split(/[\r\n]+/g).map((line) => {
                    line = line
                        .replace(/#[^#]*/g, '')
                        .trim()
                        .split(':');
                    if (line[0].trim() === 'hybridid') hybridid = (line[1] || '').trim();
                });
                if (!hybridid) throw Error('hybridid Not Found in ' + indexYAMLFile);
                return {
                    ...options,
                    hybridid,
                    bundleType: bundleType || 'biz',
                    buildTag: buildTag || '',
                    // 选取与 index.yaml 同目录下的 index.js
                    entryFile: entryFile
                };
            });
        });
    },
    /**
     * @method 校验字体文件
     * @description 继承自旧的打包工具
     */
    validateFonts: (options) => {
        qlog.debug('validateFonts():::options', options);
        let { hybridid } = options,
            fontkeyPrefix = hybridid + '_';
        return validateProject._getProvidesModuleFiles('QFontSet').then((QFontSetFiles) => {
            let allFontsArray = [];
            QFontSetFiles.forEach((FontSetFile) => {
                let QFontSetFile = path.relative(__dirname, FontSetFile);
                if (path.extname(QFontSetFile).match(/js$/g)) {
                    options.bizDependencies = (options.bizDependencies || []).concat([FontSetFile]);
                    try {
                        let allfonts = require(QFontSetFile);
                        for (let fontkey in allfonts) {
                            if (fontkey.indexOf(fontkeyPrefix) != 0 && fontkey !== hybridid) {
                                // throw new Error(
                                //     `无效的字体名'${fontkey}'. 字体的FontFamily必须等于项目的 hybridid 或者以“ hybridid_”开头。QFontSet使用帮助，请参考http://wiki.corp.qunar.com/pages/viewpage.action?pageId=105923056`
                                // );
                                console.log('[warning]', 
                                    `无效的字体名'${fontkey}'. 字体的FontFamily必须等于项目的 hybridid 或者以" hybridid_"开头。`
                                );
                            } else {
                                allFontsArray.push({
                                    fontkey: fontkey,
                                    fonturl: allfonts[fontkey]
                                });
                            }
                        }
                    } catch (e) {
                        qlog.error(`读取 ${QFontSetFile} 出错`, e);
                        throw Error('读取 ' + QFontSetFile + ' 出错 ' + e);
                    }
                    console.log('读取', QFontSetFile);
                }
            });

            return Promise.all(
                allFontsArray.map(({ fontkey, fonturl }) => {
                    return getTTFFontName(fonturl)
                        .then((fontfamilyName) => {
                            if (fontfamilyName != fontkey) {
                                console.log(
                                    '[warning]',
                                    chalk.yellow(
                                        `QFontSet内配置的字体文件不符合要求，TTF文件的FontFamily是${fontfamilyName}，但是QFontSet配置的字体名是${fontkey}`
                                    )
                                );
                                console.log(
                                    `你可以重新生成一个TTF文件，并且FontFamily为${fontkey}。QFontSet使用帮助，请参考http://wiki.corp.qunar.com/pages/viewpage.action?pageId=105923056`
                                );
                            }
                            options.__staticAssets.push(fonturl);
                        })
                        .catch((e) => {
                            console.log(
                                '[warning]',
                                chalk.yellow(`无法解析QFontSet内配置的字体文件，${e.message}，请检查。`)
                            );
                        });
                })
            ).then(() => options);
        });
    },
    runBeforeBundle: (options) => {
        qlog.debug('runBeforeBundle():::options', options);
        let outputDir = options['outputDir'];
        let qpInfo = options['qpInfo'];
        let bundleType = options['bundleType'];
        if (!fs.existsSync(outputDir)) {
            let parts = outputDir.split('/'),
                currentParts = [],
                currentPath,
                flag = false,
                i = 0,
                len = parts.length;
            if (path.isAbsolute(outputDir)) {
                i = len;
                while (i--) {
                    currentParts = parts.slice(0, i);
                    if (fs.existsSync(currentParts.join('/'))) break;
                }
            }
            for (; i < len; i++) {
                currentParts.push(parts[i]);
                if (parts[i] === '..' && !flag) continue;
                flag = true;
                currentPath = currentParts.join('/');
                if (!fs.existsSync(currentPath)) {
                    fs.mkdirSync(currentPath,{ recursive: true });
                }
            }
        }
        let isJSONString = true;
        try {
            let data = JSON.parse(qpInfo);
        } catch (e) {
            isJSONString = false;
        }
        if ((!qpInfo || !isJSONString) && bundleType !== 'platform') {
            throw new Error('缺少 qpinfo 参数或 qpinfo 参数不是 JSON 格式，请检查');
        }
        return validateProject
            .validateHybridId(options)
            .then(validateProject.copyConfig)
            .then(validateProject.relpaceEnterenceFile)
            .then(validateProject.validateFonts)
            .then((options) => {
                return validateProject._getProvidesModuleFiles('QImageSet').then((QImageSetFiles) => {
                    QImageSetFiles.forEach((QImageSetFile) => {
                        // 忽略非 *js 文件
                        if (path.extname(QImageSetFile).match(/js$/g)) {
                            try {
                                let imgsUrl = require(path.relative(__dirname, QImageSetFile));
                                for (let img in imgsUrl) {
                                    options.__staticAssets.push(imgsUrl[img]);
                                }
                            } catch (e) {
                                qlog.error(`读取 ${QImageSetFile} 出错`, e);
                                throw Error('读取 ' + QImageSetFile + ' 出错 ' + e);
                            }
                            console.log('读取', QImageSetFile);
                        }
                    });
                    options.bizDependencies = (options.bizDependencies || []).concat(QImageSetFiles);
                    return options;
                });
            })
            .then((options) =>
                utils.execSync('sh ' + path.join(__dirname, 'lib', 'clear-env.sh')).then(
                    () => options,
                    (e) => {
                        qlog.error('clear-env.sh faild', e);
                        return Error('clear-env.sh faild');
                    }
                )
            );
    },
    /**
     * @method 供 RN PACKAGER 调用，实现拆包
     */
    dependenceFilter: (obj) => {
        qlog.info('调用拆包: dependenceFilter()');
        let { dependencies, bundleType, platformDependencies, bizDependencies } = obj;
        if (bundleType === 'biz') {
            obj._dependencies = dependencies.concat([]);
            obj.dependencies = dependencies.filter((module) => {
                let i = platformDependencies.indexOf(module.path) === -1;
                // if (!i) console.log(module.path)
                return i;
            });
        } else if (bundleType === 'platform') {
            obj._dependencies = dependencies.concat([]);
            obj.dependencies = dependencies.filter((module) => {
                let i = bizDependencies.indexOf(module.path) === -1;
                // if (!i) console.log(module.path)
                return i;
            });
        }
        return obj;
    },
    /**
     * @method 构建完成后在文件尾部签名
     * @description 签名格式
     *   __SSTOKENSTRING_BIZ = "@generated SignedSource<<05341bbde5258dd723a55633e6892759>>";
     *   __BTAG_BIZ = "r-161128-171634-mengm.tian";
     */
    runAfterBundle: (options) => {
        qlog.debug('打包后: runAfterBundle()');
        let { mainModule, dependenciesFilteredPath, dependenciesFiltered, entryFile } = options,
            jsFile = options['--bundle-output'],
            buildTag = options['--build-tag'],
            qpInfo = options['--qpinfo'],
            bundleType = options['--bundle-type'],
            outputDir = options['--output-dir'],
            signature = sign('\n', {
                bundleType,
                buildTag,
                qpInfo
            }).trim(),
            prom;

        if (bundleType === 'platform') {
            sigaContent = signature;
        }

        jsFiles.push(jsFile);

        // @call FontLoader
        // if (bundleType !== 'platform') {

        //     if(options.buEntryFile) {
        //         entryFile = options.buEntryFile;
        //     }

        //     for (let i = 0, len = dependenciesFiltered.length; i < len; i++) {
        //         if (dependenciesFiltered[i].path === entryFile) {
        //             mainModule = dependenciesFiltered[i]
        //             break
        //         }
        //     }
        // }

        // @output staticAssets.json
        let assetsDest = options['--assets-dest'];
        if (!options.__staticAssets) {
            return Promise.reject('未能正确创建 staticAssets.json');
        }
        qlog.debug('写入静态文件资源到staticAssets.json供 qp 打包使用');
        fs.writeFileSync(path.join(assetsDest, 'staticAssets.json'), JSON.stringify(options.__staticAssets), {
            encoding: 'utf8'
        });

        // @output 签名
        prom = utils.readFileSync(jsFile).then((content) => {
            if (bundleType === 'platform') {
                content += `\n${signature}`;
                qlog.debug('正在追加签名');
            }

            return utils.writeFileSync(jsFile, content);
        });

        return prom;
    }
});

/**
 * @description 退出进程时的校验任务
 * --output-dir jsBundle
 * item : jsBundle/biz.flight_package_rn.ios.bundle
 */

process.on('exit', () => {
    jsFiles.map((item) => {
        const bn = path.basename(item);
        const signed = item.replace(bn, `sign.${bn}`);
        try {
            const mv = spawnSync('mv', [signed, item]);
            if (mv.status !== 0) {
                console.log(
                    '[SIGNED] spawnSync mv:',
                    `\nmv.stderr.toString(): ${mv.stderr.toString()}`,
                    `\nmv.stdout.toString(): ${mv.stdout.toString()}`
                );
            }
            let signFail = fs.readFileSync(item).indexOf(sigaContent) === -1;
            if (signFail) {
                throw new Error('签名失败, 请重试');
            } else {
                console.log(chalk.green('[SIGNED][OK]'), '签名成功');
            }
        } catch (e) {
            console.log(chalk.red('[SIGNED]'), e);
        }
    });
});
