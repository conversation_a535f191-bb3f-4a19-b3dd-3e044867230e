let { execSync } = require('child_process'),
    { readFileSync, writeFileSync, existsSync } = require('fs'),
    Promise = require('promise'),
    { basename } = require('path'),
    chalk = require('chalk'),
    path = require('path');

let utils = (module.exports = {
    handleError(e) {
        console.log(chalk.red('[ERROR]', e));
        process.exit(249);
    },
    /**
     * execSync(command[, options])
     * options <Object>
     *      cwd <string> 子进程的当前工作目录。
     * defaultConfig.getProjectRoots() 返回为 Array, 暂取第一个
     * @param cmd
     * @param options
     * @returns {Promise}
     */
    execSync: (cmd, options = { cwd: process.cwd() }) => {
        return new Promise((resolve, reject) => {
            try {
                resolve(execSync(cmd, options).toString('utf8'));
            } catch (e) {
                reject(e);
            }
        });
    },
    readFileSync: (path, options = { encoding: 'utf8' }) => {
        return new Promise((resolve, reject) => {
            try {
                resolve(readFileSync(path, options));
            } catch (e) {
                reject(e);
            }
        });
    },
    writeFileSync: (path, content, options = { encoding: 'utf8' }) => {
        return new Promise((resolve, reject) => {
            try {
                const bn = basename(path);
                writeFileSync(path.replace(bn, `sign.${bn}`), content, options);
                resolve(writeFileSync(path, content, options));
            } catch (e) {
                reject(e);
            }
        });
    },
    getEnvArg: key => {
        return key in process.env ? process.env[key] : utils.undefine;
    },
    getProjectRoots: () => {
        // QRN Begin
        // if (__dirname.match(/node_modules[\/\\]react-native[\/\\]local-cli[\/\\]util$/)) {
        // if (__dirname.match(/node_modules[\/\\]\@qnpm[\/\\]QRNPackager[\/\\]local-cli[\/\\]util$/)) {
        //     // QRN End
        //     // Packager is running from node_modules.
        //     // This is the default case for all projects created using 'react-native init'.
        //     // QRN Begin
        //     // return path.resolve(__dirname, '../../../..');
        //     return path.resolve(__dirname, '../../../../..');
        //     // QRN End
        // } else if (__dirname.match(/Pods[\/\\]React[\/\\]packager$/)) {
        //     // React Native was installed using CocoaPods.
        //     return path.resolve(__dirname, '../../../..');
        // }
        // return path.resolve(__dirname, '../..');

        let p = process.cwd();

        if (__dirname.match(/node_modules[\/\\]\@qnpm[\/\\]QRNPackager[\/\\]local-cli[\/\\]util$/)) {
            // Packager is running from node_modules.
            // This is the default case for all projects created using 'react-native init'.
            p = path.resolve(__dirname, '../../../../..');
        } else if (__dirname.match(/Pods[\/\\]React[\/\\]packager$/)) {
            // React Native was installed using CocoaPods.
            p = path.resolve(__dirname, '../../../../..');
        }

        return p;
    },

    existsSync: existsSync,
});
