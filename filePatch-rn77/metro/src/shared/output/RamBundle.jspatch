"use strict";

// QRN ADD
let createModuleFactory;
// QRN END

const Server = require("../../Server");
const asAssets = require("./RamBundle/as-assets");
const asIndexedFile = require("./RamBundle/as-indexed-file").save;
async function build(packagerClient, requestOptions) {
  // QRN ADD
  createModuleFactory = packagerClient._config.serializer.createModuleIdFactory;
  // QRN END
  const options = {
    ...Server.DEFAULT_BUNDLE_OPTIONS,
    ...requestOptions,
    bundleType: "ram",
  };
  return await packagerClient.getRamBundleInfo(options);
}
function save(bundle, options, log) {
  // QRN ADD
  if (createModuleFactory.getMapOutputDir) {
    let moduleMapPath = createModuleFactory.getMapOutputDir();

    // 同步调用 asAssets
    log('Writing assets output to:', options.assetsOutputPath);
    asAssets(bundle, options, log);
    log('Done writing assets output');
  }
  // QRN END
  return options.platform === "android" && !(options.indexedRamBundle === true)
    ? asAssets(bundle, options, log)
    : asIndexedFile(bundle, options, log);
}
exports.build = build;
exports.save = save;
exports.formatName = "bundle";
