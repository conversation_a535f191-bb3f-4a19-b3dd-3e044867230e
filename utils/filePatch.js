const fs = require('fs');
const path = require('path');
const shelljs = require('shelljs');
const walk = require('./walk');
let copyAndReplace = require('./copyAndReplace');

// 根据 TARGET_RN 判断使用不同目录
const srcPath = process.env.TARGET_RN === 'RN72'
    ? path.join(__dirname, '../filePatch-rn72/')
    : path.join(__dirname, '../filePatch-rn68/');

console.log(`当前目标 RN 版本: ${process.env.TARGET_RN}`);

// 强制覆盖顶层 RNCLI
const RN_CLI_PATH = path.join(__dirname, '../../../react-native/node_modules/@react-native-community/cli/'),
    QRN_CLI_PATH = path.join(__dirname, '../node_modules/@react-native-community/cli/'),
    TOP_CLI_PATH = path.join(__dirname, '../../../@react-native-community/cli/'),
    TOP_CLI_NODE_MODULES_PATH = path.join(__dirname, '../../../@react-native-community/cli/node_modules/');

// 删除 RN 内部的 cli 依赖版本, 使用 qrn 依赖版本
if (fs.existsSync(RN_CLI_PATH)) {
    shelljs.rm('-rf', RN_CLI_PATH);
}

if (fs.existsSync(QRN_CLI_PATH)) {
    if (!fs.existsSync(TOP_CLI_PATH)) {
        shelljs.mkdir(TOP_CLI_PATH);
    }
    shelljs.exec(`cp -r -f ${QRN_CLI_PATH}/* ${TOP_CLI_PATH}`);
    console.log('QRN 已强制覆盖 node_modules 顶层 RN-CLI 版本!');
    console.log(QRN_CLI_PATH, '====qrn-pack-tool====>', TOP_CLI_PATH);
    shelljs.rm('-rf', QRN_CLI_PATH);
}

if (fs.existsSync(TOP_CLI_NODE_MODULES_PATH)) {
    console.log('QRN 删除顶层 RN-CLI node_modules!');
    shelljs.rm('-rf', TOP_CLI_NODE_MODULES_PATH);
}

walk(srcPath).forEach((absoluteSrcFilePath) => {
    let relativeFilePath = path.relative(srcPath, absoluteSrcFilePath);

    if (relativeFilePath === 'metro/src/node-haste/DependencyGraph.jspatch') {
        const oldMetroPath = path.resolve('../../', 'metro/package.json');
        if (fs.existsSync(oldMetroPath)) {
            const oldMetroVer = require(oldMetroPath);
            if (oldMetroVer.version === '0.58.0') {
                return;
            }
        }
    }

    copyAndReplace(
        absoluteSrcFilePath,
        path.resolve('../../', dotFilePath(relativeFilePath)),
        {},
        null
    );
});

// metro
const metroArr = [
    'metro',
    'metro-cache',
    'metro-config',
    'metro-core',
    'metro-babel-register',
    'metro-babel-transformer',
    'metro-react-native-babel-preset',
    'metro-react-native-babel-transformer',
    'metro-source-map',
    'metro-symbolicate',
    'metro-minify-uglify',
    'metro-resolver',
    'metro-source-map',
    'metro-symbolicate',
];

function dotFilePath(filePath) {
    if (!filePath) return filePath;
    return filePath
        .replace('_patchnpmignore', '.npmignore')
        .replace('_patcheslintrc', '.eslintrc')
        .replace(/\.jspatch$/, '.js')
        .replace(/\.jsonpatch$/, '.json');
}