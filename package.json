{"name": "@qnpm/QRNPackager", "version": "1.0.5", "description": "Packager for Qunar React Native", "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "*************************:qrn/qrn-pack-tool.git#release"}, "engines": {"node": ">=4"}, "main": "./cli.js", "bin": {"QRNPackager": "./cli.js"}, "scripts": {"postinstall": "node utils/filePatch.js", "preinstall": "node utils/preinstall.js"}, "dependencies": {"@react-native-community/cli": "7.0.3", "@react-native-community/cli-debugger-ui": "7.0.1", "@react-native-community/cli-platform-android": "7.0.1", "@react-native-community/cli-platform-ios": "7.0.1", "@react-native-community/cli-tools": "7.0.1", "@react-native-community/cli-types": "7.0.1", "@react-native/assets": "1.0.0", "@react-native/normalize-color": "2.0.0", "@react-native/polyfills": "2.0.0", "chalk": "4.0.0", "hermes-engine": "0.11.0", "metro": "0.67.0", "metro-babel-register": "0.67.0", "metro-babel-transformer": "0.67.0", "metro-cache": "0.67.0", "metro-config": "0.67.0", "metro-core": "0.67.0", "metro-minify-terser": "0.67.0", "metro-minify-uglify": "0.67.0", "metro-react-native-babel-preset": "0.67.0", "metro-react-native-babel-transformer": "0.67.0", "metro-resolver": "0.67.0", "metro-runtime": "0.67.0", "metro-source-map": "0.67.0", "metro-symbolicate": "0.67.0", "semver": "^7.5.4", "shelljs": "^0.8.3", "tracer": "1.1.6", "yamljs": "^0.3.0", "babel-plugin-transform-remove-console": "6.9.4", "@react-native/metro-config": "0.72.11"}, "dependencies-rn72": {"@react-native-community/cli": "11.3.7", "@react-native-community/cli-debugger-ui": "11.3.7", "@react-native-community/cli-platform-android": "11.3.7", "@react-native-community/cli-platform-ios": "11.3.7", "@react-native-community/cli-tools": "11.3.7", "@react-native-community/cli-types": "11.3.7", "@react-native-community/cli-config": "11.3.7", "@react-native-community/cli-plugin-metro": "11.3.7", "@react-native-community/cli-hermes": "11.3.7", "@react-native/metro-config": "0.72.11", "@react-native/virtualized-lists": "0.72.8", "babel-plugin-transform-remove-console": "6.9.4", "chalk": "4.1.2", "hermes-engine": "0.11.0", "metro": "0.76.8", "metro-babel-register": "0.76.8", "metro-babel-transformer": "0.76.8", "metro-cache": "0.76.8", "metro-config": "0.76.8", "metro-core": "0.76.8", "metro-minify-terser": "0.76.8", "metro-minify-uglify": "0.76.8", "metro-react-native-babel-preset": "0.76.8", "metro-react-native-babel-transformer": "0.76.8", "metro-resolver": "0.76.8", "metro-runtime": "0.76.8", "metro-source-map": "0.76.8", "metro-symbolicate": "0.76.8", "semver": "^7.5.4", "shelljs": "^0.8.3", "tracer": "1.1.6", "yamljs": "^0.3.0"}, "dependencies-rn77": {"@react-native-community/cli": "15.1.3", "@react-native-community/cli-debugger-ui": "15.1.3", "@react-native-community/cli-platform-android": "15.1.3", "@react-native-community/cli-platform-ios": "15.1.3", "@react-native-community/cli-tools": "15.1.3", "@react-native-community/cli-types": "15.1.3", "@react-native-community/cli-config": "15.1.3", "@react-native-community/cli-plugin-metro": "12.3.6", "@react-native/metro-config": "0.77.1", "@react-native/babel-preset": "0.77.1", "@react-native/virtualized-lists": "0.77.1", "babel-plugin-transform-remove-console": "6.9.4", "chalk": "4.1.2", "metro": "0.81.5", "metro-babel-register": "0.81.5", "metro-babel-transformer": "0.81.5", "metro-cache": "0.81.5", "metro-config": "0.81.5", "metro-core": "0.81.5", "metro-minify-terser": "0.81.5", "metro-react-native-babel-preset": "0.77.0", "metro-react-native-babel-transformer": "0.77.0", "metro-resolver": "0.81.5", "metro-runtime": "0.81.5", "metro-source-map": "0.81.5", "metro-symbolicate": "0.81.5", "semver": "^7.5.4", "shelljs": "^0.8.3", "tracer": "1.1.6", "yamljs": "^0.3.0"}}