## Packager For QRN

### 说明

本构建工具代码承袭了 metro-bundler 官方 0.18.0 版本的逻辑，进行了定制扩展、开发，实现 QRN 的打包、拆包逻辑

本着尽量避免入侵官方代码之原则，进行此次重构，大致改动如下：

#### 新增文件

+ qrn/*

#### 修改文件

+ cli.js 「幾近全部修改」
+ local-cli/cliEntry.js
+ local-cli/commands.js
+ local-cli/core/index.js
+ local-cli/util/Config.js
+ rn-cli.config.js
+ setupBabel.js

#### 删除文件

+ packager/*
+ 改成官方的 metro-bundler 0.18.0

#### 修改说明
修改处代码示例：

```
// QRN Begin
// xxxx - 此处为注释掉的官方原逻辑
yyyy - 此处为新增的qrn逻辑
// QRN End
```


#### 要求

+ qunar-react-native 从 react-native 内独立为一个 node_modules
+ react-native-ext 内不能直接 require qunar-react-native 内通过 providesModule 提供的 module


## 发布信息:
- 工程地址: *************************:qrn/qrn-pack-tool.git
- 源码版本: r-201116-112839-guoxing.ji
- 打包人: guoxing.ji