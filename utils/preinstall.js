/**
 * This script is run before the package is installed.
 */

const fs = require('fs');
const path = require('path');

// 默认使用 RN68
const targetRN = process.env.TARGET_RN || 'RN68';
console.log(`当前目标 RN 版本: ${targetRN}`);

// 只有在 targetRN === 'RN72' 时才执行 package.json、patch、preRequire 等更新逻辑
if (targetRN === 'RN72') {

    // 更新 qrn.metro.config.js
    const templateMetroConfigPath = path.join(__dirname, '..', 'qrn.metro.config-rn72.js');
    const rootMetroConfigPath = path.join(__dirname, '..', 'qrn.metro.config.js');
    if (fs.existsSync(templateMetroConfigPath)) {
        const templateContent = fs.readFileSync(templateMetroConfigPath, 'utf8');
        fs.writeFileSync(rootMetroConfigPath, templateContent, 'utf8');
        console.log(`[RN72] 已更新 qrn.metro.config.js`);
    }

} else {
    console.log('使用默认 RN68，不执行额外替换');
}