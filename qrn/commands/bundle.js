/**
 * QRN Bundle命令
 * 整合了完整的QRN打包工作流，包括前置处理、bundle构建和后置处理
 *
 * <AUTHOR>
 * @description 统一的QRN打包工具入口
 */

const { bundleCommand } = require('@react-native/community-cli-plugin');
const path = require('path');
const chalk = require('chalk');

// 引入QRN特有的处理逻辑
const duties = require('../src/duties.js');
const utils = require('../src/utils');

// 从原始bundle命令获取配置
const originalBundleCommand = bundleCommand;
const bundleCommandLineArgs = originalBundleCommand ? originalBundleCommand.options : [];

// 自定义参数定义
const customOptions = [
  {
    name: '--output-dir <string>',
    description: '打包产物输出目录',
    default: '.'
  },
  {
    name: '--bundle-type <string>',
    description: '打包类型: biz 或 platform',
    default: 'biz'
  },
  {
    name: '--build-tag <string>',
    description: 'Git构建标签'
  },
  {
    name: '--qpinfo <string>',
    description: 'QP信息配置',
    default: '{}'
  },
  {
    name: '--pltmap-dir <string>',
    description: '平台映射目录',
    default: '.'
  },
  {
    name: '--bumap-dir <string>',
    description: '业务映射目录',
    default: '.'
  },
  {
    name: '--pltmap-output <string>',
    description: '平台映射输出目录',
    default: '.'
  },
  {
    name: '--bumap-output <string>',
    description: '业务映射输出目录',
    default: '.'
  }
];

// 合并原生bundle命令参数和自定义参数
const allOptions = [...bundleCommandLineArgs, ...customOptions];

// 获取原始bundle命令的执行函数
const originalBundleFunc = originalBundleCommand ? originalBundleCommand.func : null;

/**
 * 自定义bundle命令实现
 */
const customBundleCommand = {
  name: 'qrn-bundle',
  description: 'QRN bundle 命令，用于构建React Native bundle。',
  options: allOptions,

  func: async (argv, config, args) => {
    console.log(chalk.cyan('🚀 开始执行 QRN Bundle 命令...'));

    try {
      // 第一步：执行前置处理
      const preprocessedOptions = await executePreBundle(args);

      // 第二步：执行bundle构建
      const bundleResult = await executeBundleBuild(config, args, preprocessedOptions);

      // 第三步：执行后置处理
      await executePostBundle(preprocessedOptions);

      console.log(chalk.green('✅ QRN Bundle 构建完成'));
      return bundleResult;

    } catch (error) {
      console.error(chalk.red('❌ QRN Bundle 构建失败:'), error.message);
      utils.handleError(error);
    }
  }
};

/**
 * 执行前置处理
 */
async function executePreBundle(args) {
  console.log(chalk.blue('📋 执行前置处理...'));

  const options = {
    __staticAssets: [],
    entryFile: args.entryFile,
    bundleType: args.bundleType || 'biz',
    buildTag: args.buildTag,
    outputDir: args.outputDir || '.',
    qpInfo: args.qpinfo || '{}',
    pltMapDir: args.pltmapDir || '.',
    pltMapOutput: args.pltmapOutput || '.',
    buMapDir: args.bumapDir || '.',
    buMapOutput: args.bumapOutput || '.',
    isRamBundle: args.isRamBundle,
  };

  return await duties.runBeforeBundle(options);
}

/**
 * 执行bundle构建
 */
async function executeBundleBuild(config, args, preprocessedOptions) {
  console.log(chalk.blue('⚡ 执行bundle构建...'));

  const { bundleType, hybridid, outputDir } = preprocessedOptions;
  const platform = args.platform || 'ios';

  // 设置全局环境变量
  global.QRN_PACK_ENV = {
    platform,
    bundleType,
    plt_map_dir: preprocessedOptions.pltMapDir,
    plt_map_output: preprocessedOptions.pltMapOutput,
    bu_map_dir: preprocessedOptions.buMapDir,
    bu_map_output: preprocessedOptions.buMapOutput,
    isRamBundle: preprocessedOptions.isRamBundle,
    buildTag: preprocessedOptions.buildTag
  };

  // 确定入口文件
  let entryFile = preprocessedOptions.hasReplaceEntry
    ? preprocessedOptions.entryFile
    : args.entryFile;

  if (bundleType === 'platform') {
    entryFile = path.join(__dirname, '..', 'platform.js');
  }

  if (!entryFile) {
    throw new Error('entryFile can\'t be empty');
  }

  // 动态生成输出路径
  if (!args.bundleOutput) {
    args.bundleOutput = path.join(outputDir, `${bundleType}.${hybridid}.${platform}.bundle`);
  }

  if (!args.sourcemapOutput) {
    args.sourcemapOutput = path.join(outputDir, `${bundleType}.${hybridid}.${platform}.map`);
  }

  if (!args.assetsDest) {
    args.assetsDest = outputDir;
  }

  // 设置默认配置
  if (!args.config) {
    args.config = './qrn.metro.config.js';
  }

  // 更新参数
  args.entryFile = entryFile;
  args.platform = platform;

  console.log(chalk.gray(`📦 Bundle输出: ${args.bundleOutput}`));
  console.log(chalk.gray(`🗺️  SourceMap输出: ${args.sourcemapOutput}`));
  console.log(chalk.gray(`🖼️  Assets输出: ${args.assetsDest}`));

  // 调用原始bundle命令
  if (!originalBundleFunc) {
    throw new Error('无法找到原始bundle命令');
  }

  return await originalBundleFunc([], config, args);
}

/**
 * 执行后置处理
 */
async function executePostBundle(preprocessedOptions) {
  console.log(chalk.blue('🔧 执行后置处理...'));

  const postOptions = {
    ...preprocessedOptions,
    __staticAssets: preprocessedOptions.__staticAssets || [],
  };

  return await duties.runAfterBundle(postOptions);
}

// 导出自定义bundle命令
module.exports = { customBundleCommand };