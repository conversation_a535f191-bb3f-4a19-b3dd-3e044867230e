/**
 * <AUTHOR>
 * @description QRN PACK TOOL.
 * 本工具包含两处，一是 QRN 特有的 PACK 逻辑，其对应的配置为 *qrnOptions；二则是局部定制的 RN 原生 PACK 逻辑，其配置为 *cliOptions。
 * QRN PACK: 用以校验 index.yaml QFontSet.js QImageSet.js 等配置，以及在 PACK 结束后，注入特有的签名，并生成 生成 staticAssets.json
 * RN  PACK: 构建 JS & SOURCEMAP 产物
 */
let bundleCommandLineArgs = [
    {
      name: "--entry-file <path>",
      description:
        "Path to the root JS file, either absolute or relative to JS root",
    },
    { name: "--platform <string>", description: 'Either "ios" or "android"' },
    {
      name: "--dev [boolean]",
      description: "If false, warnings are disabled and the bundle is minified",
    },
    {
      name: "--minify [boolean]",
      description: "Allows overriding whether bundle is minified",
    },
    {
      name: "--bundle-output <string>",
      description: "File name where to store the resulting bundle",
    },
    {
      name: "--bundle-encoding <string>",
      description: "Encoding the bundle should be written in",
    },
    {
      name: "--max-workers <number>",
      description: "Specifies the maximum number of workers",
    },
    {
      name: "--sourcemap-output <string>",
      description: "File name where to store the sourcemap file",
    },
    {
      name: "--sourcemap-sources-root <string>",
      description: "Path to make sourcemap sources entries relative to",
    },
    {
      name: "--sourcemap-use-absolute-path",
      description: "Report SourceMapURL using its full path",
    },
    {
      name: "--assets-dest <string>",
      description: "Directory name where to store assets",
    },
    { name: "--reset-cache", description: "Removes cached files" },
    {
      name: "--read-global-cache",
      description: "Try to fetch transformed JS code from the global cache",
    },
    {
      name: "--config <string>",
      description: "Path to the CLI configuration file",
    },
  ],
  bundleCommandOptions = bundleCommandLineArgs.map(
    ({ name }) => name.match(/\-\-[^ \r\n\[<]+/g)[0]
  ),
  duties = require("./duties.js"),
  path = require("path"),
  chalk = require("chalk"),
  utils = require("./utils"),
  Promise = require("promise");

// 新版 QRN 打包工具自有配置及默认值
let _qrnOptions = {
  // 打包产物输出目录
  "--output-dir": ".",
  // 类型： biz or platform
  "--bundle-type": "biz",
  // '--tag-name': 'develop',
  "--qpinfo": "{}",
  "--pltmap-dir": ".",
  "--bumap-dir": ".",
  "--pltmap-output": ".",
  "--bumap-output": ".",
};
// RN 自有配置，及基于 QRN 配置生成的默认值
let _cliOptions = {
  // 平台： ios|android|iod,android
  "--platform": "ios",
  // JS 产物
  "--bundle-output": ({ hybridid, platform, bundleType, outputDir }) => {
    return path.join(outputDir, `${bundleType}.${hybridid}.${platform}.bundle`);
  },
  // SOURCE MAP 产物
  "--sourcemap-output": ({ hybridid, platform, bundleType, outputDir }) => {
    return path.join(outputDir, `${bundleType}.${hybridid}.${platform}.map`);
  },
  // 生成 staticAssets.json path
  "--assets-dest": ({ hybridid, platform, bundleType, outputDir }) => outputDir,
  "--config": "./qrn.metro.config.js",
};
// 适配旧版打包工具参数
let oldToNewMap = {
  "--sourcemap-url": "--sourcemap-output",
  "--buildTag": "--build-tag",
  "--bundleType": "--bundle-type",
};

let cli = (module.exports = {
  run: (argv, defaultCli, mainCli) => {
    let CliDescription = [],
      maxLen = 0,
      // 获取 RN CLI BUNDLE 的配置字段
      bundleCommandOptions = bundleCommandLineArgs.map(
        ({ name, description }) => {
          CliDescription.push([name, description]);
          maxLen = Math.max(maxLen, name.length);
          return name.match(/\-\-[^ \r\n\[<]+/g)[0];
        }
      );

    let start = 3,
      cmd = argv[start - 1],
      argvToPass = argv.slice(0, start - 1),
      qrnOptions = {}, // 传递给 QRN PACK TOOL 的配置
      cliOptions = {}; // 传递给 RN 原生 PACK TOOL 的配置

    // 打印使用说明
    CliDescription.unshift(
      ["--output-dir", "打包产物输出目录 默认值: ."],
      ["--bundle-type", "类型 biz or platform 默认值: biz"],
      ["--buildTag", "Git Tag 默认值: 无"]
    );
    if (cmd !== "bundle" && cmd !== "ram-bundle") {
      console.log("");
      console.log(`   使用帮助:`);
      console.log(`       node ./cli.js bundle [options]`);
      console.log(`       # options 为空等价于`);
      console.log(
        `       # node ./cli.js bundle --platform ios --bundle-output biz.demo.ios.bundle --sourcemap-output biz.demo.ios.map --assets-dest . --entry-file index.js`
      );
      console.log("");
      console.log(`    options:`);
      CliDescription.map((arr) => {
        while (arr[0].length < maxLen) {
          arr[0] += ` `;
        }
        console.log(`       ${arr[0]}`, " : " + arr[1]);
      });
      return process.exit(250);
    }

    argvToPass.push(cmd);
    for (; start < argv.length; start++) {
      let name = argv[start].trim(),
        value = argv[start + 1];
      if (name.indexOf("-") !== 0) continue;
      if (value !== cli.undefined && value.indexOf("-") === 0) {
        value = "";
      } else {
        start++;
      }
      if (name in oldToNewMap) name = oldToNewMap[name];
      if (bundleCommandOptions.indexOf(name) === -1) {
        qrnOptions[name] = value || _qrnOptions[name];
      } else {
        cliOptions[name] = value || _cliOptions[name];
      }
    }

    // 默认值设置
    for (let name in _qrnOptions) {
      if (!(name in qrnOptions)) qrnOptions[name] = _qrnOptions[name];
    }

    return duties
      .runBeforeBundle({
        __staticAssets: [],
        entryFile: cliOptions["--entry-file"],
        bundleType: qrnOptions["--bundle-type"],
        buildTag: qrnOptions["--build-tag"],
        outputDir: qrnOptions["--output-dir"],
        qpInfo: qrnOptions["--qpinfo"],
        pltMapDir: qrnOptions["--pltmap-dir"],
        pltMapOutput: qrnOptions["--pltmap-output"],
        buMapDir: qrnOptions["--bumap-dir"],
        buMapOutput: qrnOptions["--bumap-output"],
        isRamBundle: qrnOptions["--isRamBundle"],
      })
      .then((options) => {
        let { bundleType, hybridid, bizDependencies, outputDir } = options,
          platform = cliOptions["--platform"] || _cliOptions["--platform"];

        let innerProm = Promise.resolve();
        let __staticAssets = options.__staticAssets;

        // 确保 cli 默认值
        let cliOptionsCopy = {
            ...cliOptions,
            "--platform": platform,
          },
          argvToPassCopy = argvToPass.concat([]);
        for (let name in _cliOptions) {
          if (!(name in cliOptionsCopy))
            cliOptionsCopy[name] = _cliOptions[name];
        }

        // 是不是业务替换过入口
        let entryFile = options.hasReplaceEntry
            ? options.entryFile
            : cliOptionsCopy["--entry-file"],
          platformFile = path.join(__dirname, "..", "platform.js");

        if (bundleType === "platform") {
          entryFile = platformFile;
        }

        if (!entryFile) utils.handleError(Error("entryFile can't be empty"));

        cliOptionsCopy["--entry-file"] = entryFile;

        for (let name in cliOptionsCopy) {
          // Only pass options that are supported by React Native CLI bundle command
          if (bundleCommandOptions.indexOf(name) !== -1) {
            let value = cliOptionsCopy[name];
            if (typeof value === "function") {
              value = cliOptionsCopy[name] = value({
                hybridid,
                bundleType,
                platform,
                outputDir,
              });
            }
            argvToPassCopy.push(name, value);
          }
        }

        // if (configPath)
        //     let factory = require(configPath);
        console.log("[LOG]", "run local-cli/cli:", argvToPassCopy.join(" "));

        innerProm = innerProm
          .then(() => {
            return new Promise((resolve, reject) => {
              global.QRN_PACK_ENV = {
                platform,
                bundleType,
                plt_map_dir: options.pltMapDir,
                plt_map_output: options.pltMapOutput,
                bu_map_dir: options.buMapDir,
                bu_map_output: options.buMapOutput,
                isRamBundle: options.isRamBundle,
              };

              // Save original process.argv
              const originalArgv = process.argv;

              // Replace process.argv with filtered arguments
              process.argv = argvToPassCopy;

              try {
                defaultCli.run(null, () => {
                  // Restore original process.argv
                  process.argv = originalArgv;
                  resolve({
                    entryFile,
                    ...cliOptionsCopy,
                    ...qrnOptions,
                  });
                });
              } catch (error) {
                // Restore original process.argv in case of error
                process.argv = originalArgv;
                reject(error);
              }
            });
          })
          .then((options) => {
            options.qpInfo = qrnOptions["--output-dir"];
            options.__staticAssets = __staticAssets;
            return duties.runAfterBundle(options);
          });

        return innerProm;
      })
      .then(
        () => console.log(chalk.green("[SUCCESS]", "构建完成")),
        utils.handleError
      );
  },
});
